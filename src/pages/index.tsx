import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';

import StepWrapper from '../components/StepWrapper';
import Step1_Welcome from '../components/Step1_Welcome';
import Step2_DocumentUpload from '../components/Step2_DocumentUpload';
import Step3_PersonalInfo from '../components/Step3_PersonalInfo';
import Step4_TravelPurpose from '../components/Step4_TravelPurpose';
import Step5_VisaHistory from '../components/Step5_VisaHistory';
import Step6_ContactInfo from '../components/Step6_ContactInfo';
import Step7_FamilyInfo from '../components/Step7_FamilyInfo';
import Step8_EducationWork from '../components/Step8_EducationWork';
import Step9_TravelHistory from '../components/Step9_TravelHistory';

import { saveFormData, getOrCreateUserData, markWhatsappRedirected, testSupabaseConnection } from '../utils/supabase';
import {
  getOrCreateAgentId,
  getCurrentStep,
  setCurrentStep,
  isLocalStorageAvailable,
  STORAGE_KEYS
} from '../utils/localStorage';
import ThankYouPage from '../components/ThankYouPage';
import {
  VisaFormData,
  Step1Data,
  Step2Data,
  Step3Data,
  Step4Data,
  Step5Data,
  Step6Data,
  Step7Data,
  Step8Data,
  Step9Data
} from '../utils/types';

const totalSteps = 9;

export default function Home() {
  const router = useRouter();
  const [step, setStep] = useState(1);
  const [agentId, setAgentId] = useState('');
  const [isFormCompleted, setIsFormCompleted] = useState(false);
  const [isNewUser, setIsNewUser] = useState(false);
  const [formData, setFormData] = useState<VisaFormData>({
    // Step 1
    country: '',

    // Step 2
    surname: '',
    name: '',
    dateOfBirth: '',
    citizenship: '',
    passportNumber: '',
    passportIssueDate: '',
    passportExpiryDate: '',
    iin: '',
    idNumber: '',

    // Step 3
    fullNameCyrillic: '',
    hasOtherNames: false,
    otherNames: '',
    gender: '',
    maritalStatus: '',
    cityOfBirth: '',
    countryOfBirth: '',
    hasOtherCitizenship: false,
    otherCitizenship: '',
    isPermanentResidentOtherCountry: false,
    permanentResidenceCountry: '',
    nationality: '',
    hasSSN: false,
    ssn: '',
    hasTaxpayerId: false,
    taxpayerId: '',
  });

  const [uploadedFiles, setUploadedFiles] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [errorMessage, setErrorMessage] = useState('');
  const [autoSaveTimeout, setAutoSaveTimeout] = useState<NodeJS.Timeout | null>(null);
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  // Session management functions
  const initializeUserSession = async () => {
    try {
      setIsLoading(true);

      // Check localStorage availability
      if (!isLocalStorageAvailable()) {
        console.warn('localStorage is not available, using fallback mode');
      }

      // Get or create agent ID
      const urlAgentId = typeof router.query.agent_id === 'string' ? router.query.agent_id : undefined;
      const currentAgentId = getOrCreateAgentId(urlAgentId);
      setAgentId(currentAgentId);

      console.log('Agent ID initialized:', currentAgentId);

      // Test Supabase connection first
      const connectionTest = await testSupabaseConnection();
      if (!connectionTest.success) {
        console.error('Supabase connection failed:', connectionTest.error);
        setErrorMessage('Ошибка подключения к базе данных. Проверьте настройки Supabase.');
        return;
      }

      // Check if table needs to be created
      if (connectionTest.needsTableCreation) {
        console.warn('Table visa_applications does not exist. Please create it in Supabase.');
        setErrorMessage('Таблица базы данных не найдена. Обратитесь к администратору.');
        return;
      }

      // Get or create user data
      const { data, error, isNewUser: newUser } = await getOrCreateUserData(currentAgentId);

      if (error) {
        console.error('Error getting/creating user data:', error);
        setErrorMessage('Не удалось загрузить данные пользователя.');
        return;
      }

      if (data) {
        // Check if form is completed
        if (data.whatsapp_redirected) {
          console.log('Form is completed, showing thank you page');
          setIsFormCompleted(true);
          return;
        }

        // Load existing form data and step
        if (data.form_data && Object.keys(data.form_data).length > 0) {
          setFormData(data.form_data as VisaFormData);
        }

        // Set step from database or localStorage
        const dbStep = data.step_status || 1;
        const localStep = getCurrentStep();
        const currentStep = Math.max(dbStep, localStep);

        setStep(currentStep);
        setCurrentStep(currentStep); // Update localStorage

        if (data.uploaded_files) {
          setUploadedFiles(data.uploaded_files);
        }

        setIsNewUser(newUser);
        console.log(`User session initialized: ${newUser ? 'New user' : 'Existing user'}, Step: ${currentStep}`);
      }

    } catch (error) {
      console.error('Failed to initialize user session:', error);
      setErrorMessage('Не удалось инициализировать сессию пользователя.');
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-save function with debouncing
  const autoSaveFormData = async (currentFormData: VisaFormData, currentStep: number) => {
    if (!agentId || agentId.trim() === '') return;

    try {
      setIsAutoSaving(true);
      console.log('Auto-saving form data...', { agentId, step: currentStep });
      await saveFormData(currentFormData, agentId, currentStep, uploadedFiles);
      setLastSaved(new Date());
      console.log('Auto-save successful');
    } catch (error) {
      console.error('Auto-save failed:', error);
      // Don't show error to user for auto-save failures
    } finally {
      setIsAutoSaving(false);
    }
  };

  // Debounced auto-save
  const debouncedAutoSave = (currentFormData: VisaFormData, currentStep: number) => {
    if (autoSaveTimeout) {
      clearTimeout(autoSaveTimeout);
    }

    const timeout = setTimeout(() => {
      autoSaveFormData(currentFormData, currentStep);
    }, 2000); // Save after 2 seconds of inactivity

    setAutoSaveTimeout(timeout);
  };

  // Helper function to update form data and trigger auto-save
  const updateFormData = (newData: any) => {
    const updatedFormData = {
      ...formData,
      ...newData,
    };
    setFormData(updatedFormData);
    console.log('Form data updated:', newData);
  };

  // Initialize user session on component mount
  useEffect(() => {
    initializeUserSession();
  }, [router.query.agent_id]);

  // Auto-save when form data changes
  useEffect(() => {
    if (agentId && agentId.trim() !== '' && !isLoading) {
      // Check if formData has any meaningful content
      const hasContent = Object.values(formData).some(value => {
        if (typeof value === 'string') return value.trim() !== '';
        if (typeof value === 'boolean') return value;
        if (Array.isArray(value)) return value.length > 0;
        return false;
      });

      if (hasContent) {
        console.log('Form data changed, scheduling auto-save...');
        debouncedAutoSave(formData, step);
      }
    }
  }, [formData, agentId, step, isLoading, uploadedFiles]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (autoSaveTimeout) {
        clearTimeout(autoSaveTimeout);
      }
    };
  }, [autoSaveTimeout]);

  const handleNext = async () => {
    if (step === totalSteps) {
      try {
        // Redirect to WhatsApp
        const phoneNumber = '+77064172408';
        await markWhatsappRedirected(agentId);
        window.location.href = `https://wa.me/${phoneNumber}?text=Я заполнил(а) анкету. Мой ID: ${agentId}`;
      } catch (error) {
        console.error('Failed to redirect to WhatsApp:', error);
        setErrorMessage('Не удалось перейти в WhatsApp. Пожалуйста, попробуйте еще раз.');
      }
    } else {
      const nextStep = step + 1;
      setStep(nextStep);
      setCurrentStep(nextStep); // Save to localStorage
    }
  };

  const handlePrev = () => {
    const prevStep = Math.max(1, step - 1);
    setStep(prevStep);
    setCurrentStep(prevStep); // Save to localStorage
  };

  // Type-safe step submit handlers
  const handleStep1Submit = async (stepData: Step1Data) => {
    try {
      updateFormData(stepData);

      // Save immediately when moving to next step
      const updatedFormData = { ...formData, ...stepData };
      await saveFormData(updatedFormData, agentId, step + 1, uploadedFiles);
      handleNext();
    } catch (error) {
      console.error('Failed to save form data:', error);
      setErrorMessage('Не удалось сохранить данные. Пожалуйста, попробуйте еще раз.');
    }
  };

  const handleStep2Submit = async (stepData: Step2Data) => {
    try {
      updateFormData(stepData);

      // Save immediately when moving to next step
      const updatedFormData = { ...formData, ...stepData };
      await saveFormData(updatedFormData, agentId, step + 1, uploadedFiles);
      handleNext();
    } catch (error) {
      console.error('Failed to save form data:', error);
      setErrorMessage('Не удалось сохранить данные. Пожалуйста, попробуйте еще раз.');
    }
  };

  const handleStep3Submit = async (stepData: Step3Data) => {
    try {
      updateFormData(stepData);

      // Save immediately when moving to next step
      const updatedFormData = { ...formData, ...stepData };
      await saveFormData(updatedFormData, agentId, step + 1, uploadedFiles);
      handleNext();
    } catch (error) {
      console.error('Failed to save form data:', error);
      setErrorMessage('Не удалось сохранить данные. Пожалуйста, попробуйте еще раз.');
    }
  };

  const handleStep4Submit = async (stepData: Step4Data) => {
    try {
      updateFormData(stepData);

      // Save immediately when moving to next step
      const updatedFormData = { ...formData, ...stepData };
      await saveFormData(updatedFormData, agentId, step + 1, uploadedFiles);
      handleNext();
    } catch (error) {
      console.error('Failed to save form data:', error);
      setErrorMessage('Не удалось сохранить данные. Пожалуйста, попробуйте еще раз.');
    }
  };

  const handleStep5Submit = async (stepData: Step5Data) => {
    try {
      updateFormData(stepData);

      // Save immediately when moving to next step
      const updatedFormData = { ...formData, ...stepData };
      await saveFormData(updatedFormData, agentId, step + 1, uploadedFiles);
      handleNext();
    } catch (error) {
      console.error('Failed to save form data:', error);
      setErrorMessage('Не удалось сохранить данные. Пожалуйста, попробуйте еще раз.');
    }
  };

  const handleStep6Submit = async (stepData: Step6Data) => {
    try {
      updateFormData(stepData);

      // Save immediately when moving to next step
      const updatedFormData = { ...formData, ...stepData };
      await saveFormData(updatedFormData, agentId, step + 1, uploadedFiles);
      handleNext();
    } catch (error) {
      console.error('Failed to save form data:', error);
      setErrorMessage('Не удалось сохранить данные. Пожалуйста, попробуйте еще раз.');
    }
  };

  const handleStep7Submit = async (stepData: Step7Data) => {
    try {
      updateFormData(stepData);

      // Save immediately when moving to next step
      const updatedFormData = { ...formData, ...stepData };
      await saveFormData(updatedFormData, agentId, step + 1, uploadedFiles);
      handleNext();
    } catch (error) {
      console.error('Failed to save form data:', error);
      setErrorMessage('Не удалось сохранить данные. Пожалуйста, попробуйте еще раз.');
    }
  };

  const handleStep8Submit = async (stepData: Step8Data) => {
    try {
      updateFormData(stepData);

      // Save immediately when moving to next step
      const updatedFormData = { ...formData, ...stepData };
      await saveFormData(updatedFormData, agentId, step + 1, uploadedFiles);
      handleNext();
    } catch (error) {
      console.error('Failed to save form data:', error);
      setErrorMessage('Не удалось сохранить данные. Пожалуйста, попробуйте еще раз.');
    }
  };

  const handleStep9Submit = async (stepData: Step9Data) => {
    try {
      updateFormData(stepData);

      const updatedFormData = {
        ...formData,
        ...stepData,
      };

      // Save immediately when completing the form
      await saveFormData(updatedFormData, agentId, step + 1, uploadedFiles);

      // Send WhatsApp message to user's phone after form completion
      if (updatedFormData.phone) {
        try {
          console.log('Sending WhatsApp message to:', updatedFormData.phone);
          const response = await fetch('/api/send-whatsapp', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              phone: updatedFormData.phone
            })
          });

          const result = await response.json();

          if (!result.success) {
            console.error('Failed to send WhatsApp message to user:', result.error);
            // Don't block the flow if message sending fails
          } else {
            console.log('WhatsApp message sent to user successfully:', result);
          }
        } catch (error) {
          console.error('Error sending WhatsApp message to user:', error);
          // Don't block the flow if message sending fails
        }
      } else {
        console.log('No phone number found, skipping WhatsApp message');
      }

      handleNext();
    } catch (error) {
      console.error('Failed to save form data:', error);
      setErrorMessage('Не удалось сохранить данные. Пожалуйста, попробуйте еще раз.');
    }
  };

  // Determine the current step title
  const getStepTitle = () => {
    switch (step) {
      case 1:
        return 'Добро пожаловать';
      case 2:
        return 'Загрузка документов';
      case 3:
        return 'Личная информация';
      case 4:
        return 'Цель поездки';
      case 5:
        return 'История визы';
      case 6:
        return 'Контактная информация';
      case 7:
        return 'Информация о семье';
      case 8:
        return 'Образование и работа';
      case 9:
        return 'История поездок';
      default:
        return 'Анкета на визу';
    }
  };

  // Determine which form component to render based on the current step
  const renderStepContent = () => {
    switch (step) {
      case 1:
        return (
          <Step1_Welcome
            initialValues={{ country: formData.country }}
            onSubmit={handleStep1Submit}
          />
        );
      case 2:
        return (
          <Step2_DocumentUpload
            initialValues={{
              surname: formData.surname,
              name: formData.name,
              dateOfBirth: formData.dateOfBirth,
              citizenship: formData.citizenship,
              passportNumber: formData.passportNumber,
              passportIssueDate: formData.passportIssueDate,
              passportExpiryDate: formData.passportExpiryDate,
              iin: formData.iin,
              idNumber: formData.idNumber,
            }}
            onSubmit={handleStep2Submit}
            uploadedFiles={uploadedFiles}
            setUploadedFiles={setUploadedFiles}
          />
        );
      case 3:
        return (
          <Step3_PersonalInfo
            initialValues={{
              fullNameCyrillic: formData.fullNameCyrillic,
              hasOtherNames: formData.hasOtherNames,
              otherNames: formData.otherNames,
              gender: formData.gender,
              maritalStatus: formData.maritalStatus,
              cityOfBirth: formData.cityOfBirth,
              countryOfBirth: formData.countryOfBirth,
              hasOtherCitizenship: formData.hasOtherCitizenship,
              otherCitizenship: formData.otherCitizenship,
              isPermanentResidentOtherCountry: formData.isPermanentResidentOtherCountry,
              permanentResidenceCountry: formData.permanentResidenceCountry,
              nationality: formData.nationality,
              hasSSN: formData.hasSSN,
              ssn: formData.ssn,
              hasTaxpayerId: formData.hasTaxpayerId,
              taxpayerId: formData.taxpayerId,
            }}
            onSubmit={handleStep3Submit}
          />
        );
      case 4:
        return (
          <Step4_TravelPurpose
            initialValues={{
              travelPurpose: formData.travelPurpose || '',
              travelWithOthers: formData.travelWithOthers || false,
              travelAsGroup: formData.travelAsGroup,
              groupName: formData.groupName,
              companions: formData.companions || [],
            }}
            onSubmit={handleStep4Submit}
          />
        );
      case 5:
        return (
          <Step5_VisaHistory
            initialValues={{
              hasBeenToUSA: formData.hasBeenToUSA || false,
              hasUSVisa: formData.hasUSVisa || false,
              lastVisaDate: formData.lastVisaDate,
              visaNumber: formData.visaNumber,
              isSameVisaType: formData.isSameVisaType,
              isSameCountry: formData.isSameCountry,
              hasVisaRejections: formData.hasVisaRejections || false,
              rejectionVisaType: formData.rejectionVisaType,
              rejectionDate: formData.rejectionDate,
            }}
            onSubmit={handleStep5Submit}
          />
        );
      case 6:
        return (
          <Step6_ContactInfo
            initialValues={{
              address: formData.address || '',
              city: formData.city || '',
              country: formData.country || '',
              zipCode: formData.zipCode || '',
              phone: formData.phone || '',
              email: formData.email || '',
              socialMediaLinks: formData.socialMediaLinks || [],
            }}
            onSubmit={handleStep6Submit}
          />
        );
      case 7:
        return (
          <Step7_FamilyInfo
            initialValues={{
              fatherSurname: formData.fatherSurname || '',
              fatherName: formData.fatherName || '',
              fatherDateOfBirth: formData.fatherDateOfBirth,
              isFatherDateOfBirthUnknown: formData.isFatherDateOfBirthUnknown || false,
              isFatherInUSA: formData.isFatherInUSA || false,
              fatherUSAReason: formData.fatherUSAReason,
              motherSurname: formData.motherSurname || '',
              motherName: formData.motherName || '',
              motherDateOfBirth: formData.motherDateOfBirth,
              isMotherDateOfBirthUnknown: formData.isMotherDateOfBirthUnknown || false,
              isMotherInUSA: formData.isMotherInUSA || false,
              motherUSAReason: formData.motherUSAReason,
              hasRelativesInUSA: formData.hasRelativesInUSA || false,
              relatives: formData.relatives || [],
            }}
            onSubmit={handleStep7Submit}
          />
        );
      case 8:
        return (
          <Step8_EducationWork
            initialValues={{
              occupation: formData.occupation || '',
              // Employment fields
              companyName: formData.companyName,
              position: formData.position,
              workAddress: formData.workAddress,
              workPhone: formData.workPhone,
              workExperience: formData.workExperience,
              income: formData.income,
              // Student fields
              universityName: formData.universityName,
              universityAddress: formData.universityAddress,
              faculty: formData.faculty,
              startDate: formData.startDate,
              endDate: formData.endDate,
              // Business fields
              businessType: formData.businessType,
              businessName: formData.businessName,
              businessRegistrationType: formData.businessRegistrationType,
              businessRegistrationNumber: formData.businessRegistrationNumber,
              businessRegistrationDate: formData.businessRegistrationDate,
              businessActivity: formData.businessActivity,
              monthlyBusinessIncome: formData.monthlyBusinessIncome,
              hasEmployees: formData.hasEmployees || false,
              employeeCount: formData.employeeCount,
              businessStatus: formData.businessStatus,
              businessAddress: formData.businessAddress,
              businessWebsite: formData.businessWebsite,
              hasInternationalClients: formData.hasInternationalClients || false,
              hasPermanentContracts: formData.hasPermanentContracts || false,
              paysTaxes: formData.paysTaxes || false,
              businessExperienceYears: formData.businessExperienceYears,
              hasBankStatements: formData.hasBankStatements || false,
              yearlyIncome: formData.yearlyIncome,
              hasOffice: formData.hasOffice || false,
              officeAddress: formData.officeAddress,
            }}
            onSubmit={handleStep8Submit}
          />
        );
      case 9:
        return (
          <Step9_TravelHistory
            initialValues={{
              visitedCountries: formData.visitedCountries || [],
            }}
            onSubmit={handleStep9Submit}
          />
        );
      default:
        return (
          <div className="text-center p-8">
            <h2 className="text-xl font-semibold">Шаг в разработке</h2>
            <p className="text-gray-600 mt-2">Этот раздел анкеты пока находится в разработке.</p>
            <button
              className="btn-primary mt-4"
              onClick={handleNext}
            >
              Продолжить
            </button>
          </div>
        );
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        <p className="ml-3 text-gray-700">Загрузка данных...</p>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>Анкета на визу - {getStepTitle()}</title>
        <meta name="description" content="Заполните анкету для получения визы" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="container mx-auto px-4 py-8">
        {/* Debug info - remove in production */}
        {agentId && (
          <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-2 rounded mb-4 text-sm">
            <strong>Agent ID:</strong> {agentId}
            {isAutoSaving && (
              <span className="ml-4 text-orange-600">
                <span className="animate-pulse">●</span> Сохранение...
              </span>
            )}
            {lastSaved && !isAutoSaving && (
              <span className="ml-4 text-green-600">
                ✓ Сохранено: {lastSaved.toLocaleTimeString()}
              </span>
            )}
          </div>
        )}

        {errorMessage && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {errorMessage}
          </div>
        )}

        <StepWrapper
          title={getStepTitle()}
          step={step}
          totalSteps={totalSteps}
          onNext={handleNext}
          onPrev={handlePrev}
          canGoNext={true} // This should be determined by form validation in a real app
        >
          {renderStepContent()}
        </StepWrapper>
      </main>
    </>
  );
}